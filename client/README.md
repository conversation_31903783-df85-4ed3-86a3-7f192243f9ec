# RK3588 参数设置客户端

这是一个用于 RK3588 设备参数设置的 Web 客户端应用，使用 Qt 兼容的 UDP 通信协议，支持实时参数读取和修改。

## ✨ 主要特性

- 🌐 **现代化 Web 界面**: 响应式设计，支持参数搜索和中文映射
- 🔄 **双服务器架构**: 同时连接默认服务器和自定义服务器，提供冗余保障
- ⚙️ **在线配置管理**: 通过 Web 界面实时修改配置，无需重启
- 📡 **Qt 兼容 UDP 通信**: 与下位机进行参数读写，完全兼容 Qt 程序协议
- 📤 **参数映射上传**: 支持上传 JSON 格式的参数中文映射文件
- 🔍 **智能搜索**: 支持按参数名或中文描述搜索
- 🐳 **容器化部署**: Docker 部署，支持热重载开发
- 📊 **实时状态监控**: 实时显示服务器连接状态和设备信息

## 🚀 快速开始

### 配置参数

编辑 `.env` 文件修改配置：

```bash
# Web服务端口
WEB_PORT=7002

# 自定义服务器配置（默认服务器**************:7777自动启用）
SERVER_HOST=*************
SERVER_PORT=8888

# 设备配置
DEVICE_ID=太阳能通信车001

# 下位机配置
UDP_TARGET_IP=*************
UDP_TARGET_PORT=8080
UDP_PORT=18001


```

### 启动服务

```bash
# 使用启动脚本
./start.sh

# 或手动启动
docker-compose up -d
```

### 访问界面

- **主界面**: http://localhost:7002 - 参数设置和管理
- **配置管理**: http://localhost:7002/config.html - 在线配置修改
- **健康检查**: http://localhost:7002/health - 服务状态检查
- **API 文档**: http://localhost:7002/docs - 自动生成的 API 文档

## 📁 项目结构

```
client/
├── .env                      # 环境配置文件
├── docker-compose.yml        # 容器编排配置
├── Dockerfile               # 容器构建文件
├── requirements.txt         # Python依赖
├── start.sh                # 启动脚本
├── parameter_mappings.json  # 参数中文映射文件
├── src/                    # 源代码目录
│   ├── main.py            # 主程序入口
│   ├── web_server.py      # Web服务器和API
│   ├── server_client.py   # 双服务器连接管理
│   ├── qt_udp_handler.py  # Qt兼容UDP通信
│   ├── qt_compatible_udp.py # Qt协议实现
│   └── log_manager.py     # 日志管理
└── static/                # 静态文件
    ├── index.html         # 主界面
    ├── config.html        # 配置管理界面
    ├── style.css          # 样式文件
    ├── script.js          # 主界面JavaScript
    └── config.js          # 配置界面JavaScript
```

## ⚙️ 核心功能

### 🌐 Web 界面功能

- **参数管理**: 实时读取、修改下位机参数
- **智能搜索**: 支持按参数名或中文描述搜索
- **参数映射**: 上传 JSON 文件显示参数中文名称
- **在线配置**: 通过 Web 界面修改系统配置
- **状态监控**: 实时显示连接状态和设备信息

### 🔄 双服务器架构

- **默认服务器**: **************:7777（固定启用）
- **自定义服务器**: 通过配置文件设置的服务器
- **冗余连接**: 两个服务器独立连接，提供故障转移
- **心跳监控**: 定期检测服务器连接状态
- **自动重连**: 连接断开时自动重新连接

### 📡 通信协议

- **Qt 兼容 UDP**: 完全兼容 Qt 程序的 UDP 通信协议
- **实时通信**: 与下位机进行实时参数读写
- **协议解析**: 自动解析 Qt 格式的参数数据
- **错误处理**: 完善的通信错误处理机制

## 🔧 配置说明

### 配置方式

1. **Web 界面配置**（推荐）: 访问 http://localhost:7002/config.html
2. **文件配置**: 直接编辑 `.env` 文件

### 配置参数

| 配置项          | 说明                  | 默认值           |
| --------------- | --------------------- | ---------------- |
| WEB_PORT        | Web 服务端口          | 7002             |
| SERVER_HOST     | 自定义服务器 IP 地址  | *************    |
| SERVER_PORT     | 自定义服务器 TCP 端口 | 8888             |
| DEVICE_ID       | 设备唯一标识          | 太阳能通信车 001 |
| UDP_TARGET_IP   | 下位机 IP 地址        | *************    |
| UDP_TARGET_PORT | 下位机 UDP 端口       | 8080             |
| UDP_PORT        | 客户端 UDP 监听端口   | 18001            |


### 服务器配置

- **默认服务器**: **************:7777（固定，无需配置）
- **自定义服务器**: 通过 SERVER_HOST 和 SERVER_PORT 配置
- **双服务器**: 系统同时连接两个服务器，提供冗余保障

## 📖 使用指南

### 参数管理

1. 访问主界面：http://localhost:7002
2. 点击"读取所有参数"获取下位机参数
3. 在搜索框中输入参数名或中文描述进行搜索
4. 修改参数值后点击"写入"按钮保存到下位机

### 参数映射上传

1. 准备 JSON 格式的映射文件，格式如下：
   ```
   // sleepTm 休眠时间
   // max_rpm 电机最大转速
   // min_voltage 最小电压
   ```
2. 点击"上传"按钮选择映射文件
3. 上传成功后参数名后会显示对应的中文描述

### 配置管理

1. 访问配置界面：http://localhost:7002/config.html
2. 修改所需配置项
3. 点击"保存配置"，配置立即生效无需重启
4. 查看服务器连接状态和设备信息

## 🛠️ 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f client

# 停止服务
docker-compose down

# 重启服务（应用配置更改）
docker-compose restart

# 重新构建并启动（代码更改）
docker-compose down && docker-compose build && docker-compose up -d

# 进入容器调试
docker-compose exec client bash

# 查看容器内日志文件
docker-compose exec client tail -f logs/client.log
```

## 🔍 故障排除

### 端口冲突

```bash
# 检查端口占用
netstat -tlnp | grep 7002

# 修改端口（在配置界面或.env文件中）
WEB_PORT=7003
```

### 服务器连接问题

```bash
# 测试默认服务器连接
telnet ************** 7777

# 测试自定义服务器连接
telnet ************* 8888

# 查看服务器连接状态
curl http://localhost:7002/api/server-status
```

### 下位机通信问题

```bash
# 测试下位机网络连通性
ping *************

# 检查UDP端口
netstat -ulnp | grep 18001

# 查看UDP通信日志
docker-compose logs -f | grep udp
```

### 配置问题

```bash
# 查看当前配置
curl http://localhost:7002/api/config

# 重新加载配置（通过Web界面）
# 访问 http://localhost:7002/config.html 点击"保存配置"

# 查看环境变量
docker-compose exec client env | grep -E "(WEB_PORT|SERVER_HOST|DEVICE_ID)"
```

### 日志分析

```bash
# 查看所有日志
docker-compose logs -f

# 查看特定组件日志
docker-compose logs -f | grep server_client  # 服务器连接日志
docker-compose logs -f | grep qt_udp         # UDP通信日志
docker-compose logs -f | grep web_server     # Web服务器日志

# 查看容器内详细日志
docker-compose exec client tail -f logs/client.log
```

## 📋 系统要求

- Docker 20.10+
- docker-compose 1.29+
- 内存: 至少 512MB
- 网络: 能访问服务器和下位机

docker-compose down && docker-compose build --no-cache && docker-compose up -d
