<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理 - RK3588参数设置客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-50%) translateX(-5px);
        }

        .content {
            padding: 30px;
        }

        .log-info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #4facfe;
        }

        .log-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1.1em;
            color: #333;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 234, 167, 0.4);
        }

        .backup-files {
            margin-top: 30px;
        }

        .backup-files h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .file-list {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            max-height: 350px;
            /* 限制最大高度，大约5个文件项的高度 */
            overflow-y: auto;
            /* 垂直滚动条 */
            border: 1px solid #e9ecef;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            transition: background 0.2s ease;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .file-details {
            font-size: 0.9em;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .hidden {
            display: none;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .editable {
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            position: relative;
        }

        .editable:hover {
            background: #e9ecef;
            color: #4facfe;
        }

        .editable::after {
            content: "✏️";
            font-size: 0.8em;
            opacity: 0;
            margin-left: 8px;
            transition: opacity 0.2s ease;
        }

        .editable:hover::after {
            opacity: 1;
        }

        .edit-input {
            width: 100%;
            padding: 4px 8px;
            border: 2px solid #4facfe;
            border-radius: 4px;
            font-size: 1.1em;
            outline: none;
        }

        .edit-select {
            width: 100%;
            padding: 4px 8px;
            border: 2px solid #4facfe;
            border-radius: 4px;
            font-size: 1.1em;
            outline: none;
            background: white;
        }

        .log-content-section {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #28a745;
        }

        .log-content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .log-file-info {
            flex: 1;
        }

        .log-file-info span:first-child {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .log-file-details {
            display: block;
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .log-content-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .lines-select {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        .log-content-container {
            background: #2d3748;
            border-radius: 8px;
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
        }

        .log-content-text {
            color: #e2e8f0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .file-item {
            cursor: pointer;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-item.active {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        /* 自定义滚动条样式 */
        .file-list::-webkit-scrollbar {
            width: 8px;
        }

        .file-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .file-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .file-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 为火狐浏览器提供滚动条样式 */
        .file-list {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        .scroll-hint {
            font-size: 0.8em;
            color: #666;
            font-weight: normal;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 0.6;
            }

            50% {
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .back-btn {
                position: static;
                transform: none;
                margin-bottom: 20px;
                align-self: flex-start;
            }

            .header {
                text-align: left;
            }

            .log-info-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-btn">
                ← 返回主页
            </a>
            <h1>📋 日志管理</h1>
            <p>管理系统日志文件和轮转设置</p>
        </div>

        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载日志信息...</p>
            </div>

            <div id="alert-container"></div>

            <div id="log-content" class="hidden">
                <div class="log-info-card">
                    <h3>📊 当前日志状态</h3>
                    <div class="log-info-grid">
                        <div class="info-item">
                            <div class="info-label">当前日志文件</div>
                            <div class="info-value" id="current-log-file">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">文件大小</div>
                            <div class="info-value" id="current-size">-</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="usage-progress"></div>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最大大小限制</div>
                            <div class="info-value editable" id="max-size" data-field="max_size_mb" title="点击编辑">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">备份文件数量</div>
                            <div class="info-value editable" id="backup-count" data-field="backup_count" title="点击编辑">-
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">使用率</div>
                            <div class="info-value" id="usage-percent">-</div>
                        </div>
                    </div>

                    <div class="actions">
                        <button class="btn btn-primary" id="refresh-btn">
                            🔄 刷新信息
                        </button>
                        <button class="btn btn-warning" id="rotate-btn">
                            🔄 手动轮转
                        </button>
                    </div>
                </div>

                <div class="backup-files">
                    <h3>📁 备份文件列表 <span class="scroll-hint" id="scroll-hint" style="display: none;">↕️ 可滚动查看更多</span>
                    </h3>
                    <div class="file-list" id="backup-file-list">
                        <!-- 备份文件列表将在这里动态生成 -->
                    </div>
                </div>

                <div class="log-content-section" id="log-content-section" style="display: none;">
                    <h3>📄 日志内容</h3>
                    <div class="log-content-header">
                        <div class="log-file-info">
                            <span id="current-log-filename">-</span>
                            <span class="log-file-details" id="log-file-details">-</span>
                        </div>
                        <div class="log-content-controls">
                            <select id="lines-select" class="lines-select">
                                <option value="50">最后50行</option>
                                <option value="100" selected>最后100行</option>
                                <option value="200">最后200行</option>
                                <option value="500">最后500行</option>
                                <option value="0">全部内容</option>
                            </select>
                            <button id="refresh-log-btn" class="btn btn-small">🔄 刷新</button>
                            <button id="close-log-btn" class="btn btn-small">✖️ 关闭</button>
                        </div>
                    </div>
                    <div class="log-content-container">
                        <pre id="log-content-text" class="log-content-text">正在加载日志内容...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="log-manager.js"></script>
</body>

</html>