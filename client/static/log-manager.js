// 日志管理页面JavaScript

class LogManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadLogInfo();
    }

    bindEvents() {
        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadLogInfo();
        });

        // 手动轮转按钮
        document.getElementById('rotate-btn').addEventListener('click', () => {
            this.rotateLog();
        });



        // 日志内容控制按钮
        document.getElementById('refresh-log-btn').addEventListener('click', () => {
            this.refreshLogContent();
        });

        document.getElementById('close-log-btn').addEventListener('click', () => {
            this.closeLogContent();
        });

        document.getElementById('lines-select').addEventListener('change', () => {
            this.refreshLogContent();
        });
    }

    async loadLogInfo(silent = false) {
        try {
            this.showLoading(true);
            if (!silent) {
                this.clearAlert();
            }

            const response = await fetch('/api/log-info');
            const result = await response.json();

            if (result.success) {
                this.displayLogInfo(result.data);
                if (!silent) {
                    this.showAlert('日志信息加载成功', 'success');
                }
            } else {
                throw new Error(result.message || '获取日志信息失败');
            }
        } catch (error) {
            console.error('加载日志信息失败:', error);
            this.showAlert(`加载日志信息失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displayLogInfo(data) {
        // 只更新备份文件列表
        this.displayBackupFiles(data.backup_files || []);
    }

    displayBackupFiles(backupFiles) {
        const container = document.getElementById('backup-file-list');

        // 添加当前日志文件到列表顶部
        let filesHtml = `
            <div class="file-item" data-filename="client.log" onclick="globalLogManager.showLogContent('client.log')">
                <div class="file-info">
                    <div class="file-name">📄 client.log (当前日志)</div>
                    <div class="file-details">
                        正在使用的日志文件 | 点击查看内容
                    </div>
                </div>
            </div>
        `;

        if (backupFiles.length === 0) {
            filesHtml += `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-name">暂无备份文件</div>
                        <div class="file-details">当前日志文件尚未达到轮转条件</div>
                    </div>
                </div>
            `;
        } else {
            filesHtml += backupFiles.map(file => `
                <div class="file-item" data-filename="${file.filename}" onclick="globalLogManager.showLogContent('${file.filename}')">
                    <div class="file-info">
                        <div class="file-name">📄 ${file.filename}</div>
                        <div class="file-details">
                            大小: ${file.size_mb.toFixed(2)} MB |
                            修改时间: ${file.modified_time} | 点击查看内容
                        </div>
                    </div>
                </div>
            `).join('');
        }

        container.innerHTML = filesHtml;

        // 检查是否需要显示滚动提示
        this.checkScrollHint(container);
    }

    async rotateLog() {
        try {
            this.showLoading(true);
            this.clearAlert();

            const response = await fetch('/api/log-rotate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('日志轮转执行成功！新的日志文件已创建。', 'success');
                // 延迟刷新信息，让轮转操作完成
                setTimeout(() => {
                    this.loadLogInfo();
                }, 1000);
            } else {
                throw new Error(result.message || '日志轮转失败');
            }
        } catch (error) {
            console.error('日志轮转失败:', error);
            this.showAlert(`日志轮转失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        const content = document.getElementById('log-content');

        if (show) {
            loading.classList.remove('hidden');
            content.classList.add('hidden');
        } else {
            loading.classList.add('hidden');
            content.classList.remove('hidden');
        }
    }

    showAlert(message, type = 'success') {
        const container = document.getElementById('alert-container');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

        container.innerHTML = `
            <div class="alert ${alertClass}">
                ${type === 'success' ? '✅' : '❌'} ${message}
            </div>
        `;

        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                this.clearAlert();
            }, 3000);
        }
    }

    clearAlert() {
        document.getElementById('alert-container').innerHTML = '';
    }



    async showLogContent(filename) {
        try {
            // 显示日志内容区域
            const section = document.getElementById('log-content-section');
            section.style.display = 'block';

            // 更新文件名显示
            document.getElementById('current-log-filename').textContent = filename;

            // 高亮选中的文件
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-filename="${filename}"]`)?.classList.add('active');

            // 加载日志内容
            await this.loadLogContent(filename);

            // 滚动到日志内容区域
            section.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.error('显示日志内容失败:', error);
            this.showAlert(`显示日志内容失败: ${error.message}`, 'error');
        }
    }

    async loadLogContent(filename) {
        try {
            const lines = document.getElementById('lines-select').value;
            const contentElement = document.getElementById('log-content-text');

            contentElement.textContent = '正在加载日志内容...';

            const response = await fetch(`/api/log-content/${filename}?lines=${lines}`);
            const result = await response.json();

            if (result.success) {
                const data = result.data;

                // 更新文件详情
                document.getElementById('log-file-details').textContent =
                    `文件大小: ${this.formatFileSize(data.file_size)} | 总行数: ${data.total_lines} | 显示: ${data.displayed_lines} 行`;

                // 显示日志内容
                contentElement.textContent = data.content || '日志文件为空';

                // 滚动到底部（显示最新内容）
                contentElement.scrollTop = contentElement.scrollHeight;

            } else {
                contentElement.textContent = `加载失败: ${result.message}`;
                this.showAlert(`加载日志内容失败: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('加载日志内容失败:', error);
            document.getElementById('log-content-text').textContent = `加载失败: ${error.message}`;
            this.showAlert(`加载日志内容失败: ${error.message}`, 'error');
        }
    }

    refreshLogContent() {
        const filename = document.getElementById('current-log-filename').textContent;
        if (filename && filename !== '-') {
            this.loadLogContent(filename);
        }
    }

    closeLogContent() {
        document.getElementById('log-content-section').style.display = 'none';

        // 取消文件高亮
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    checkScrollHint(container) {
        const scrollHint = document.getElementById('scroll-hint');
        if (!scrollHint) return;

        // 检查内容是否超出容器高度
        if (container.scrollHeight > container.clientHeight) {
            scrollHint.style.display = 'inline';

            // 3秒后自动隐藏提示
            setTimeout(() => {
                scrollHint.style.display = 'none';
            }, 3000);
        } else {
            scrollHint.style.display = 'none';
        }
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 格式化时间
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// 全局日志管理器实例
let globalLogManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    globalLogManager = new LogManager();
});

// 定期刷新日志信息（每30秒）
setInterval(() => {
    if (document.visibilityState === 'visible' && globalLogManager) {
        globalLogManager.loadLogInfo(true); // 静默刷新，不显示成功消息
    }
}, 30000);
