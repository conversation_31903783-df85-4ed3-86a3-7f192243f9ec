services:
  client:
    build: .
    container_name: parameter_client
    network_mode: "host"
    restart: always
    environment:
      - TZ=Asia/Shanghai
      - WEB_PORT=${WEB_PORT}
      - SERVER_HOST=${SERVER_HOST}
      - SERVER_PORT=${SERVER_PORT}
      - DEVICE_ID=${DEVICE_ID}
      - UDP_TARGET_IP=${UDP_TARGET_IP}
      - UDP_TARGET_PORT=${UDP_TARGET_PORT}
      - UDP_PORT=${UDP_PORT}
      - LOG_LEVEL=${LOG_LEVEL}
    volumes:
      - ./logs:/app/logs
      - ./static:/app/static
      - ./src:/app/src
      - ./.env:/app/.env
      - ./parameter_mappings.json:/app/parameter_mappings.json
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${WEB_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3

version: "3.8"
